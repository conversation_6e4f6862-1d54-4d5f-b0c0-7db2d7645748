//@version=5
strategy("增强型Vegas通道策略", overlay=true, margin_long=100, margin_short=100)

// 参数配置（参考网页1的EMA设置）
ema144 = ta.ema(close, 144)
ema169 = ta.ema(close, 169)
ema12 = ta.ema(close, 12)
ema576 = ta.ema(close, 576)
ema676 = ta.ema(close, 676)
atr_length = input.int(14, "ATR周期")
atr_mult = input.float(1.1, "ATR倍数", step=0.1)

// 可视化设置（改进网页3的显示方案）
plot(ema144, color=color.new(#FF9800, 0), linewidth=2, title="EMA144")
plot(ema169, color=color.new(#2196F3, 0), linewidth=2, title="EMA169")
plot(ema12, color=color.new(#4CAF50, 0), linewidth=1, title="EMA12")
plot(ema576, color=color.new(#9C27B0, 30), linewidth=3, title="EMA576")
plot(ema676, color=color.new(#607D8B, 30), linewidth=3, title="EMA676")

// 核心条件判断（结合网页1的过滤逻辑）
longCondition1 = close > ema144 and close > ema169  // 价格在隧道上方
longCondition2 = ema144 > ema576 and ema144 > ema676 and ema169 > ema576 and ema169 > ema676  // 隧道处于长期均线上方
longCondition3 = ta.crossover(ema12, ema144) and ta.crossover(ema12, ema169)  // 双重金叉确认

shortCondition1 = close < ema144 and close < ema169  // 价格在隧道下方
shortCondition2 = ema144 < ema576 and ema144 < ema676 and ema169 < ema576 and ema169 < ema676  // 隧道处于长期均线下方
shortCondition3 = ta.crossunder(ema12, ema144) and ta.crossunder(ema12, ema169)  // 双重死叉确认

// 完整开仓条件（参考网页2的趋势过滤）
longCondition = longCondition1 and longCondition2 and longCondition3
shortCondition = shortCondition1 and shortCondition2 and shortCondition3

// 动态止盈止损（采用网页3的ATR风控方案）
atr = ta.atr(atr_length)
trailOffset = atr * atr_mult

// 交易执行模块（优化网页1的仓位管理）
if (longCondition)
    strategy.entry("Long", strategy.long)
    strategy.exit("TP/Long", "Long", trail_points=close*0.01, trail_offset=trailOffset)

if (shortCondition)
    strategy.entry("Short", strategy.short) 
    strategy.exit("TP/Short", "Short", trail_points=close*0.01, trail_offset=trailOffset)

// 可视化增强（融合网页1和网页3的信号标记）
plotshape(series=longCondition, style=shape.triangleup, color=color.green, location=location.belowbar, size=size.small, title="多单信号")
plotshape(series=shortCondition, style=shape.triangledown, color=color.red, location=location.abovebar, size=size.small, title="空单信号")

// 背景色趋势提示（参考网页2的多空分界）
bgcolor(longCondition ? color.new(color.green, 90) : shortCondition ? color.new(color.red, 90) : na)