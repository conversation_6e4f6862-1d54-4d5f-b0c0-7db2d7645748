# TradingView加密货币动量策略 - OKX自动交易系统
# 环境变量配置示例文件
# 复制此文件为 .env 并填入您的实际配置

# =============================================================================
# OKX交易所API配置
# =============================================================================

# OKX API密钥（从OKX交易所获取）
OKX_API_KEY=your_okx_api_key_here

# OKX Secret密钥（从OKX交易所获取）
OKX_SECRET_KEY=your_okx_secret_key_here

# OKX Passphrase（创建API时设置的密码）
OKX_PASSPHRASE=your_okx_passphrase_here

# 交易环境标志 (0: 实盘交易, 1: 模拟交易)
OKX_FLAG=1

# OKX API基础URL（通常不需要修改）
OKX_BASE_URL=https://www.okx.com

# =============================================================================
# Webhook服务器配置
# =============================================================================

# 服务器监听端口
PORT=5000

# 服务器监听地址
HOST=0.0.0.0

# Webhook验证密钥（用于验证TradingView请求的安全性）
WEBHOOK_SECRET=your_webhook_secret_key_here

# =============================================================================
# 安全配置
# =============================================================================

# JWT密钥（如果使用JWT认证）
JWT_SECRET=your_jwt_secret_here

# 允许的IP地址列表（逗号分隔，留空表示允许所有IP）
ALLOWED_IPS=

# API请求频率限制（每分钟最大请求数）
RATE_LIMIT=60

# =============================================================================
# 交易配置
# =============================================================================

# 默认交易对
DEFAULT_SYMBOL=BTC-USDT

# 最大仓位大小（USDT）
MAX_POSITION_SIZE=1000

# 最小交易金额（USDT）
MIN_TRADE_AMOUNT=10

# 默认风险百分比
DEFAULT_RISK_PERCENT=1.0

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/trading.log

# 是否启用控制台日志
CONSOLE_LOGGING=true

# =============================================================================
# 监控和通知配置
# =============================================================================

# 是否启用邮件通知
EMAIL_NOTIFICATIONS=false

# SMTP服务器配置（如果启用邮件通知）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# 通知邮箱地址
NOTIFICATION_EMAIL=<EMAIL>

# 是否启用Telegram通知
TELEGRAM_NOTIFICATIONS=false

# Telegram Bot Token（如果启用Telegram通知）
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# Telegram Chat ID
TELEGRAM_CHAT_ID=your_telegram_chat_id

# =============================================================================
# 数据库配置（可选）
# =============================================================================

# 数据库类型 (sqlite, mysql, postgresql)
DATABASE_TYPE=sqlite

# SQLite数据库文件路径
SQLITE_DB_PATH=data/trading.db

# MySQL/PostgreSQL配置（如果使用）
# DATABASE_URL=mysql://user:password@localhost:3306/trading_db
# DATABASE_URL=postgresql://user:password@localhost:5432/trading_db

# =============================================================================
# 开发和调试配置
# =============================================================================

# 是否启用调试模式
DEBUG=false

# 是否启用详细日志
VERBOSE_LOGGING=false

# 测试模式（不执行实际交易）
TEST_MODE=true

# =============================================================================
# 性能监控配置
# =============================================================================

# 是否启用性能监控
PERFORMANCE_MONITORING=false

# Prometheus监控端口
PROMETHEUS_PORT=9090

# 健康检查端点
HEALTH_CHECK_ENDPOINT=/health

# =============================================================================
# 备份和恢复配置
# =============================================================================

# 自动备份间隔（小时）
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份存储路径
BACKUP_PATH=backups/
