// © Kilo Code
//@version=5
strategy("MA Cross BB ATR Strategy", 
         overlay=true, 
         initial_capital=10000, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=10, 
         commission_type=strategy.commission.percent, 
         commission_value=0.04)

// --- Inputs ---
// MA Settings
ma_fast_len = input.int(50, title="Fast MA Length")
ma_slow_len = input.int(200, title="Slow MA Length")
ma_filter_len = input.int(7, title="Filter MA Length")

// Bollinger Bands Settings
bb_len = input.int(20, title="BB Length")
bb_mult = input.float(2.0, title="BB Multiplier")

// ATR Settings
atr_len = input.int(14, title="ATR Length")
atr_mult = input.float(1.5, title="ATR SL Multiplier")

// --- Indicator Calculations ---
// Moving Averages
ma_fast = ta.sma(close, ma_fast_len)
ma_slow = ta.sma(close, ma_slow_len)
ma_filter = ta.sma(close, ma_filter_len)

// Bollinger Bands
[bb_middle, bb_upper, bb_lower] = ta.bb(close, bb_len, bb_mult)

// ATR
atr_val = ta.atr(atr_len)

// --- Entry Conditions ---
// Crossover/Crossunder signals
long_entry_signal = ta.crossover(ma_fast, ma_slow)
short_entry_signal = ta.crossunder(ma_fast, ma_slow)

// Filter conditions
long_filter_confirm = close > bb_middle and close > ma_filter
short_filter_confirm = close < bb_middle and close < ma_filter

// Final entry conditions
enter_long = long_entry_signal and long_filter_confirm
enter_short = short_entry_signal and short_filter_confirm

// --- Stop Loss Calculation ---
long_stop_price = close - (atr_val * atr_mult)
short_stop_price = close + (atr_val * atr_mult)

// --- Strategy Execution ---
if (enter_long)
    strategy.entry("Enter Long", strategy.long)
    strategy.exit("SL/Exit Long", from_entry="Enter Long", stop=long_stop_price)
    // Close any existing short position before entering long
    strategy.close("Enter Short", comment="Close Short for Long")

if (enter_short)
    strategy.entry("Enter Short", strategy.short)
    strategy.exit("SL/Exit Short", from_entry="Enter Short", stop=short_stop_price)
    // Close any existing long position before entering short
    strategy.close("Enter Long", comment="Close Long for Short")

// --- Plotting ---
// MAs
plot(ma_fast, color=color.new(color.green, 0), title="Fast MA")
plot(ma_slow, color=color.new(color.red, 0), title="Slow MA")
plot(ma_filter, color=color.new(color.yellow, 50), title="Filter MA", style=plot.style_circles, linewidth=1)

// BBands
p_bb_upper = plot(bb_upper, color=color.new(color.blue, 50), title="BB Upper")
p_bb_lower = plot(bb_lower, color=color.new(color.blue, 50), title="BB Lower")
fill(p_bb_upper, p_bb_lower, color=color.new(color.blue, 95), title="BB Fill")
plot(bb_middle, color=color.new(color.white, 50), title="BB Middle")

// --- Webhook Alert Section (to be configured later) ---
// For now, we create the alert conditions
alertcondition(condition=enter_long, title="Enter Long Signal", message="Symbol={{ticker}}, Action=Buy, Price={{close}}")
alertcondition(condition=enter_short, title="Enter Short Signal", message="Symbol={{ticker}}, Action=Sell, Price={{close}}")
alertcondition(condition=ta.crossunder(ma_fast, ma_slow), title="Exit Long Signal", message="Symbol={{ticker}}, Action=Close, Position=Long")
alertcondition(condition=ta.crossover(ma_fast, ma_slow), title="Exit Short Signal", message="Symbol={{ticker}}, Action=Close, Position=Short")