{"name": "tradingview-okx-crypto-strategy", "version": "1.0.0", "description": "TradingView加密货币动量策略 - OKX自动交易系统", "main": "webhook_server.js", "scripts": {"start": "node webhook_server.js", "dev": "nodemon webhook_server.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["tradingview", "okx", "cryptocurrency", "trading", "webhook", "automation", "pine-script", "bitcoin", "ethereum"], "author": "Crypto Trading Strategy Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.5.0", "crypto": "^1.0.1", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "moment": "^2.29.4", "lodash": "^4.17.21", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "prettier": "^3.0.1", "@types/node": "^20.4.8"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tradingview-okx-crypto-strategy.git"}, "bugs": {"url": "https://github.com/your-username/tradingview-okx-crypto-strategy/issues"}, "homepage": "https://github.com/your-username/tradingview-okx-crypto-strategy#readme"}