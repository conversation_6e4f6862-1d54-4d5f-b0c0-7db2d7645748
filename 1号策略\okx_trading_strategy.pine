// © TraeAI
//@version=5
strategy("OKX Webhook自动化交易策略", 
         overlay=true, 
         initial_capital=1000, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=10, 
         commission_type=strategy.commission.percent, 
         commission_value=0.075)

// --- 输入参数 ---
fast_ma_len = input.int(10, title="快速EMA周期")
slow_ma_len = input.int(30, title="慢速EMA周期")
rsi_len = input.int(14, title="RSI周期")
rsi_overbought = input.int(70, title="RSI 超买阈值")
rsi_oversold = input.int(30, title="RSI 超卖阈值")

// --- 指标计算 ---
fast_ma = ta.ema(close, fast_ma_len)
slow_ma = ta.ema(close, slow_ma_len)
rsi = ta.rsi(close, rsi_len)

// --- 交易逻辑 ---
long_condition = ta.crossover(fast_ma, slow_ma) and rsi < rsi_overbought
short_condition = ta.crossunder(fast_ma, slow_ma) and rsi > rsi_oversold

// --- 仓位管理 ---
if (long_condition)
    strategy.entry("开多", strategy.long)

if (short_condition)
    strategy.entry("开空", strategy.short)

// --- 可视化 ---
plot(fast_ma, color=color.new(color.green, 0), title="快速EMA")
plot(slow_ma, color=color.new(color.red, 0), title="慢速EMA")

// --- Webhook 警报设置 ---
// 这是一个JSON格式的示例，您需要根据您的Webhook服务商调整
// {{strategy.order.action}} - "buy" 或 "sell"
// {{strategy.position_size}} - 仓位大小
// {{ticker}} - 交易对，例如 BTCUSDT

webhook_message_long = '{"exchange":"okx", "symbol":"' + syminfo.ticker + '", "side":"buy", "type":"market", "amount":"' + str.tostring(strategy.equity / close) + '", "secret":"YOUR_WEBHOOK_SECRET"}'
webhook_message_short = '{"exchange":"okx", "symbol":"' + syminfo.ticker + '", "side":"sell", "type":"market", "amount":"' + str.tostring(strategy.equity / close) + '", "secret":"YOUR_WEBHOOK_SECRET"}'

// --- 创建警报条件 ---
if long_condition
    alert(webhook_message_long, alert.freq_once_per_bar_close)

if short_condition
    alert(webhook_message_short, alert.freq_once_per_bar_close)