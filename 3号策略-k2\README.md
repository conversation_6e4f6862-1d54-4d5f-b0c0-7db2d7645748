# TradingView 策略: 布林带 + EMA + RSI

这是一个为 TradingView Pine Script v5 设计的加密货币合约交易策略。它结合了布林带、指数移动平均线 (EMA) 和相对强弱指数 (RSI) 来识别交易机会，并集成了通过 Webhook 与 OKX 交易所进行自动交易的功能。

## 策略逻辑

该策略采用三重过滤系统来生成交易信号：

1.  **趋势过滤**: 使用中期 (20) 和长期 (50) EMA 来确定市场的主要趋势。
    *   **多头趋势**: 20 EMA > 50 EMA
    *   **空头趋势**: 20 EMA < 50 EMA

2.  **动量过滤**: 使用 RSI (14) 来衡量市场的动量。
    *   **多头动量**: RSI > 50
    *   **空头动量**: RSI < 50

3.  **波动性突破**: 使用布林带 (20, 2) 来识别波动性突破。
    *   **多头突破**: 价格上穿布林带上轨。
    *   **空头突破**: 价格下穿布林带下轨。

4.  **短期确认**: 使用短期 (5) EMA 作为最终的突破确认。
    *   **多头确认**: 价格 > 5 EMA
    *   **空头确认**: 价格 < 5 EMA

### 开仓条件

*   **开多**: 当同时满足多头趋势、多头动量、多头波动性突破和多头短期确认时。
*   **开空**: 当同时满足空头趋势、空头动量、空头波动性突破和空头短期确认时。

### 风险管理

*   **止损**: 使用 ATR (14) 的 1.5 倍进行动态止损。
*   **止盈**: 基于 1:2 的风险回报比，即 ATR 的 3.0 倍。

## 如何使用

1.  **在 TradingView 中打开**:
    *   打开 TradingView 图表。
    *   点击底部面板的 "Pine 编辑器"。
    *   将 `bollinger_ema_rsi_strategy.pine` 文件的代码复制并粘贴到编辑器中。
    *   点击 "添加到图表"。

2.  **配置策略**:
    *   在图表上，点击策略名称旁边的 "设置" 图标。
    *   根据您的偏好调整输入参数（如布林带长度、EMA 周期、RSI 设置、ATR 乘数和杠杆）。

3.  **回测**:
    *   在 Pine 编辑器下方，切换到 "策略测试器" 选项卡。
    *   在这里，您可以查看策略在历史数据上的表现，包括净利润、胜率、最大回撤等。

## OKX Webhook 集成

要实现自动化交易，您需要设置一个 Webhook，将 TradingView 的警报发送到 OKX 交易所。这通常需要一个第三方中间服务来解析 TradingView 的警报并将其转换为 OKX API 可以理解的格式。

### 步骤

1.  **选择一个第三方服务**:
    *   有许多服务可以实现这一功能，例如 3Commas, Alertatron, PineConnector 等。您需要注册并订阅其中一个服务。

2.  **在第三方服务中设置**:
    *   将您的 OKX 账户通过 API 密钥连接到所选服务。
    *   在该服务中，您将获得一个唯一的 Webhook URL。

3.  **在 TradingView 中创建警报**:
    *   在 TradingView 图表上，点击顶部工具栏的 "警报" 按钮。
    *   在 "条件" 下拉菜单中，选择您的策略脚本名称。
    *   选择 "任何 alert() 函数调用"。
    *   在 "通知" 选项卡下，勾选 "Webhook URL" 并粘贴您从第三方服务获得的 URL。
    *   **消息正文**: 策略脚本已经为您生成了 JSON 格式的消息。您无需在此处填写。当 `go_long` 或 `go_short` 条件满足时，脚本会自动触发包含以下信息的警报：
        ```json
        {
          "symbol": "SYMBOL", 
          "side": "buy/sell", 
          "type": "market", 
          "quantity_percent": "10", 
          "leverage": "5", 
          "sl": "stop_loss_price", 
          "tp": "take_profit_price"
        }
        ```
    *   点击 "创建"。

4.  **启动自动交易**:
    *   一旦警报被触发，TradingView 将向您的 Webhook URL 发送一个 POST 请求。
    *   第三方服务将接收此请求，解析 JSON 消息，并根据您的设置在 OKX 上执行相应的交易。

**免责声明**: 自动交易存在风险。在使用真实资金之前，请务必在模拟账户中进行充分的测试。确保您了解所使用策略和第三方服务的所有方面。