# OKX交易所集成指南

## 概述

本指南详细说明如何将TradingView策略信号与OKX交易所进行集成，实现自动交易功能。

## 前置条件

1. OKX交易所账户
2. 已完成KYC认证
3. 启用API交易权限
4. 充足的账户余额
5. 已配置的Webhook接收服务器

## OKX API配置

### 1. 创建API密钥

1. 登录OKX交易所
2. 进入"API管理"页面
3. 点击"创建API"
4. 设置以下权限：
   - ✅ 读取权限
   - ✅ 交易权限
   - ❌ 提现权限（安全考虑）

### 2. API密钥信息

创建成功后，您将获得：
- **API Key**: 用于身份识别
- **Secret Key**: 用于签名验证
- **Passphrase**: 额外的安全密码

⚠️ **重要**: 请妥善保管这些信息，不要泄露给任何人。

### 3. IP白名单设置

为了提高安全性，建议设置IP白名单：
1. 在API管理页面点击"编辑"
2. 添加您的服务器IP地址
3. 保存设置

## 支持的交易对

本策略支持以下主要加密货币交易对：

| TradingView符号 | OKX交易对 | 最小交易量 |
|----------------|-----------|------------|
| BTCUSDT | BTC-USDT | 0.00001 BTC |
| ETHUSDT | ETH-USDT | 0.001 ETH |
| SOLUSDT | SOL-USDT | 0.01 SOL |
| XRPUSDT | XRP-USDT | 1 XRP |

## Webhook服务器实现

### 1. Python Flask示例

```python
from flask import Flask, request, jsonify
import okx.Trade as Trade
import okx.Account as Account
import json
import hmac
import hashlib

app = Flask(__name__)

# OKX API配置
API_KEY = "your_api_key"
SECRET_KEY = "your_secret_key"
PASSPHRASE = "your_passphrase"
FLAG = "0"  # 0: 实盘, 1: 模拟盘

# 初始化OKX客户端
tradeAPI = Trade.TradeAPI(API_KEY, SECRET_KEY, PASSPHRASE, False, FLAG)
accountAPI = Account.AccountAPI(API_KEY, SECRET_KEY, PASSPHRASE, False, FLAG)

@app.route('/api/trading/signals', methods=['POST'])
def handle_trading_signal():
    try:
        # 验证请求
        data = request.get_json()
        
        # 解析交易信号
        action = data.get('action')
        symbol = data.get('symbol')
        size = float(data.get('size', 0))
        price = float(data.get('price', 0))
        
        # 执行交易
        if action == 'buy':
            result = execute_buy_order(symbol, size)
        elif action == 'sell':
            result = execute_sell_order(symbol, size)
        elif action == 'close':
            result = close_position(symbol)
        else:
            return jsonify({'error': 'Invalid action'}), 400
            
        return jsonify({'status': 'success', 'result': result})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def execute_buy_order(symbol, size):
    """执行买入订单"""
    result = tradeAPI.place_order(
        instId=symbol,
        tdMode="cross",  # 全仓模式
        side="buy",
        ordType="market",
        sz=str(size)
    )
    return result

def execute_sell_order(symbol, size):
    """执行卖出订单"""
    result = tradeAPI.place_order(
        instId=symbol,
        tdMode="cross",
        side="sell", 
        ordType="market",
        sz=str(size)
    )
    return result

def close_position(symbol):
    """平仓操作"""
    # 获取当前持仓
    positions = accountAPI.get_positions(instId=symbol)
    
    for pos in positions['data']:
        if float(pos['pos']) != 0:
            side = "sell" if pos['posSide'] == "long" else "buy"
            result = tradeAPI.place_order(
                instId=symbol,
                tdMode="cross",
                side=side,
                ordType="market",
                sz=abs(float(pos['pos']))
            )
    return result

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### 2. Node.js Express示例

```javascript
const express = require('express');
const axios = require('axios');
const crypto = require('crypto');

const app = express();
app.use(express.json());

// OKX API配置
const API_KEY = 'your_api_key';
const SECRET_KEY = 'your_secret_key';
const PASSPHRASE = 'your_passphrase';
const BASE_URL = 'https://www.okx.com';

// 生成签名
function generateSignature(timestamp, method, requestPath, body) {
    const message = timestamp + method + requestPath + body;
    return crypto.createHmac('sha256', SECRET_KEY).update(message).digest('base64');
}

// 执行OKX API请求
async function okxRequest(method, endpoint, data = {}) {
    const timestamp = new Date().toISOString();
    const requestPath = `/api/v5${endpoint}`;
    const body = method === 'GET' ? '' : JSON.stringify(data);
    
    const signature = generateSignature(timestamp, method, requestPath, body);
    
    const headers = {
        'OK-ACCESS-KEY': API_KEY,
        'OK-ACCESS-SIGN': signature,
        'OK-ACCESS-TIMESTAMP': timestamp,
        'OK-ACCESS-PASSPHRASE': PASSPHRASE,
        'Content-Type': 'application/json'
    };
    
    const config = {
        method,
        url: BASE_URL + requestPath,
        headers,
        data: method === 'GET' ? undefined : data
    };
    
    return await axios(config);
}

// 处理交易信号
app.post('/api/trading/signals', async (req, res) => {
    try {
        const { action, symbol, size, price } = req.body;
        
        let result;
        if (action === 'buy') {
            result = await placeBuyOrder(symbol, size);
        } else if (action === 'sell') {
            result = await placeSellOrder(symbol, size);
        } else if (action === 'close') {
            result = await closePosition(symbol);
        }
        
        res.json({ status: 'success', result });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

async function placeBuyOrder(symbol, size) {
    return await okxRequest('POST', '/trade/order', {
        instId: symbol,
        tdMode: 'cross',
        side: 'buy',
        ordType: 'market',
        sz: size.toString()
    });
}

async function placeSellOrder(symbol, size) {
    return await okxRequest('POST', '/trade/order', {
        instId: symbol,
        tdMode: 'cross',
        side: 'sell',
        ordType: 'market',
        sz: size.toString()
    });
}

async function closePosition(symbol) {
    // 获取持仓信息
    const positions = await okxRequest('GET', `/account/positions?instId=${symbol}`);
    
    for (const pos of positions.data.data) {
        if (parseFloat(pos.pos) !== 0) {
            const side = pos.posSide === 'long' ? 'sell' : 'buy';
            await okxRequest('POST', '/trade/order', {
                instId: symbol,
                tdMode: 'cross',
                side: side,
                ordType: 'market',
                sz: Math.abs(parseFloat(pos.pos)).toString()
            });
        }
    }
}

app.listen(5000, () => {
    console.log('Webhook server running on port 5000');
});
```

## 安全配置

### 1. 环境变量配置

```bash
# .env文件
OKX_API_KEY=your_api_key
OKX_SECRET_KEY=your_secret_key
OKX_PASSPHRASE=your_passphrase
OKX_FLAG=0
WEBHOOK_SECRET=your_webhook_secret
```

### 2. 签名验证

```python
def verify_webhook_signature(payload, signature, secret):
    """验证Webhook签名"""
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(signature, expected_signature)
```

### 3. 风险控制

```python
def validate_trading_signal(data):
    """验证交易信号"""
    # 检查交易对是否支持
    supported_symbols = ['BTC-USDT', 'ETH-USDT', 'SOL-USDT', 'XRP-USDT']
    if data.get('symbol') not in supported_symbols:
        raise ValueError('Unsupported trading pair')
    
    # 检查交易量是否合理
    size = float(data.get('size', 0))
    if size <= 0 or size > MAX_POSITION_SIZE:
        raise ValueError('Invalid position size')
    
    # 检查价格是否合理
    price = float(data.get('price', 0))
    if price <= 0:
        raise ValueError('Invalid price')
    
    return True
```

## 部署建议

### 1. 服务器要求
- **操作系统**: Ubuntu 20.04 LTS或更高版本
- **内存**: 最少2GB RAM
- **存储**: 最少20GB SSD
- **网络**: 稳定的互联网连接，低延迟

### 2. 使用Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "webhook_server.py"]
```

### 3. 使用PM2管理进程

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start webhook_server.py --name "okx-webhook"

# 设置开机自启
pm2 startup
pm2 save
```

## 监控和日志

### 1. 交易日志记录

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading.log'),
        logging.StreamHandler()
    ]
)

def log_trade_execution(signal_data, result):
    """记录交易执行日志"""
    logging.info(f"Trade executed: {signal_data} -> {result}")
```

### 2. 健康检查端点

```python
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 检查OKX API连接
        account_info = accountAPI.get_account_balance()
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'api_status': 'connected'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500
```

## 故障排除

### 1. 常见错误代码

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| 50001 | API密钥无效 | 检查API密钥配置 |
| 50004 | 签名验证失败 | 检查签名算法实现 |
| 50013 | 系统繁忙 | 实施重试机制 |
| 51008 | 余额不足 | 检查账户余额 |

### 2. 调试技巧

```python
# 启用调试模式
import logging
logging.basicConfig(level=logging.DEBUG)

# 打印API请求详情
def debug_api_request(method, endpoint, data):
    print(f"API Request: {method} {endpoint}")
    print(f"Data: {json.dumps(data, indent=2)}")
```

## 下一步

完成OKX集成后，请参考：
- [风险管理文档](risk_management.md) - 设置风险控制措施
- [回测配置说明](backtesting_guide.md) - 优化策略参数
- [Webhook配置文档](webhook_setup.md) - 完善警报设置
