//@version=5
strategy("加密货币动量策略 - OKX自动交易", shorttitle="Crypto Momentum", overlay=true, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=10, 
         commission_type=strategy.commission.percent, commission_value=0.1)

// ============================================================================
// 策略参数设置
// ============================================================================

// 技术指标参数
ema_fast_length = input.int(12, title="EMA快线周期", minval=1, maxval=50, group="技术指标")
ema_slow_length = input.int(26, title="EMA慢线周期", minval=1, maxval=100, group="技术指标")
rsi_length = input.int(14, title="RSI周期", minval=1, maxval=50, group="技术指标")
rsi_oversold = input.int(30, title="RSI超卖线", minval=10, maxval=40, group="技术指标")
rsi_overbought = input.int(70, title="RSI超买线", minval=60, maxval=90, group="技术指标")
macd_fast = input.int(12, title="MACD快线", minval=1, maxval=50, group="技术指标")
macd_slow = input.int(26, title="MACD慢线", minval=1, maxval=100, group="技术指标")
macd_signal = input.int(9, title="MACD信号线", minval=1, maxval=20, group="技术指标")
atr_length = input.int(14, title="ATR周期", minval=1, maxval=50, group="技术指标")

// 风险管理参数
risk_percent = input.float(1.0, title="单笔风险百分比", minval=0.1, maxval=5.0, step=0.1, group="风险管理")
stop_loss_atr = input.float(2.0, title="止损ATR倍数", minval=0.5, maxval=5.0, step=0.1, group="风险管理")
take_profit_atr = input.float(3.0, title="止盈ATR倍数", minval=1.0, maxval=10.0, step=0.1, group="风险管理")
trailing_stop = input.bool(true, title="启用跟踪止损", group="风险管理")
max_drawdown = input.float(10.0, title="最大回撤限制(%)", minval=1.0, maxval=50.0, step=1.0, group="风险管理")

// 时间过滤参数
use_time_filter = input.bool(false, title="启用时间过滤", group="时间过滤")
start_hour = input.int(9, title="开始小时(UTC)", minval=0, maxval=23, group="时间过滤")
end_hour = input.int(21, title="结束小时(UTC)", minval=0, maxval=23, group="时间过滤")

// Webhook参数
webhook_enabled = input.bool(true, title="启用Webhook", group="Webhook设置")
symbol_name = input.string("BTC-USDT", title="交易对名称", group="Webhook设置")

// ============================================================================
// 技术指标计算
// ============================================================================

// EMA均线
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)

// RSI
rsi = ta.rsi(close, rsi_length)

// MACD
[macd_line, signal_line, macd_histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// ATR
atr = ta.atr(atr_length)

// 布林带（辅助指标）
bb_length = 20
bb_mult = 2.0
bb_basis = ta.sma(close, bb_length)
bb_dev = bb_mult * ta.stdev(close, bb_length)
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev

// ============================================================================
// 交易信号逻辑
// ============================================================================

// 趋势确认
bullish_trend = ema_fast > ema_slow
bearish_trend = ema_fast < ema_slow

// EMA交叉信号
ema_bullish_cross = ta.crossover(ema_fast, ema_slow)
ema_bearish_cross = ta.crossunder(ema_fast, ema_slow)

// RSI条件
rsi_not_overbought = rsi < rsi_overbought
rsi_not_oversold = rsi > rsi_oversold
rsi_neutral = rsi_not_overbought and rsi_not_oversold

// MACD条件
macd_bullish = macd_histogram > 0 and macd_histogram > macd_histogram[1]
macd_bearish = macd_histogram < 0 and macd_histogram < macd_histogram[1]

// 价格位置确认
price_above_ema_slow = close > ema_slow
price_below_ema_slow = close < ema_slow

// 时间过滤
time_ok = true
if use_time_filter
    current_hour = hour(time, "UTC")
    time_ok := current_hour >= start_hour and current_hour <= end_hour

// ============================================================================
// 入场条件
// ============================================================================

// 多头入场条件
long_condition = ema_bullish_cross and rsi_neutral and macd_bullish and time_ok and strategy.position_size == 0

// 空头入场条件  
short_condition = ema_bearish_cross and rsi_neutral and macd_bearish and time_ok and strategy.position_size == 0

// ============================================================================
// 风险管理和仓位计算
// ============================================================================

// 计算仓位大小
calculate_position_size(entry_price, stop_loss_price) =>
    risk_amount = strategy.equity * risk_percent / 100
    price_diff = math.abs(entry_price - stop_loss_price)
    position_size = risk_amount / price_diff
    position_size

// 动态止损止盈价格
long_stop_loss = close - (atr * stop_loss_atr)
long_take_profit = close + (atr * take_profit_atr)
short_stop_loss = close + (atr * stop_loss_atr)
short_take_profit = close - (atr * take_profit_atr)

// ============================================================================
// 交易执行
// ============================================================================

// 多头入场
if long_condition
    pos_size = calculate_position_size(close, long_stop_loss)
    strategy.entry("Long", strategy.long, qty=pos_size)
    strategy.exit("Long Exit", "Long", stop=long_stop_loss, limit=long_take_profit, 
                  trail_points=trailing_stop ? atr * stop_loss_atr : na, 
                  trail_offset=trailing_stop ? atr * 0.5 : na)

// 空头入场
if short_condition
    pos_size = calculate_position_size(close, short_stop_loss)
    strategy.entry("Short", strategy.short, qty=pos_size)
    strategy.exit("Short Exit", "Short", stop=short_stop_loss, limit=short_take_profit,
                  trail_points=trailing_stop ? atr * stop_loss_atr : na,
                  trail_offset=trailing_stop ? atr * 0.5 : na)

// 紧急平仓条件（最大回撤保护）
if strategy.openprofit / strategy.equity * 100 < -max_drawdown
    strategy.close_all("Emergency Close")

// ============================================================================
// Webhook Alert消息
// ============================================================================

// 多头开仓消息
long_open_message = '{"action":"buy","symbol":"' + symbol_name + '","type":"market","size":"{{strategy.position_size}}","price":"{{close}}","timestamp":"{{time}}","strategy":"crypto_momentum","stop_loss":"' + str.tostring(long_stop_loss) + '","take_profit":"' + str.tostring(long_take_profit) + '"}'

// 空头开仓消息
short_open_message = '{"action":"sell","symbol":"' + symbol_name + '","type":"market","size":"{{strategy.position_size}}","price":"{{close}}","timestamp":"{{time}}","strategy":"crypto_momentum","stop_loss":"' + str.tostring(short_stop_loss) + '","take_profit":"' + str.tostring(short_take_profit) + '"}'

// 平仓消息
close_message = '{"action":"close","symbol":"' + symbol_name + '","type":"market","size":"{{strategy.position_size}}","price":"{{close}}","timestamp":"{{time}}","strategy":"crypto_momentum"}'

// ============================================================================
// Alert条件设置
// ============================================================================

if webhook_enabled
    // 多头开仓Alert
    alertcondition(long_condition, title="多头开仓", message=long_open_message)
    
    // 空头开仓Alert
    alertcondition(short_condition, title="空头开仓", message=short_open_message)
    
    // 平仓Alert（这里简化处理，实际应该根据具体平仓原因设置）
    alertcondition(ta.change(strategy.position_size) != 0 and strategy.position_size == 0, 
                   title="平仓", message=close_message)

// ============================================================================
// 图表显示
// ============================================================================

// 绘制EMA均线
plot(ema_fast, color=color.blue, linewidth=2, title="EMA快线")
plot(ema_slow, color=color.red, linewidth=2, title="EMA慢线")

// 绘制布林带
p1 = plot(bb_upper, color=color.gray, linewidth=1, title="布林带上轨")
p2 = plot(bb_lower, color=color.gray, linewidth=1, title="布林带下轨")
fill(p1, p2, color=color.new(color.gray, 95), title="布林带填充")

// 标记交易信号
plotshape(long_condition, title="多头信号", location=location.belowbar, 
          style=shape.triangleup, size=size.normal, color=color.green)
plotshape(short_condition, title="空头信号", location=location.abovebar, 
          style=shape.triangledown, size=size.normal, color=color.red)

// 显示止损止盈线
plot(strategy.position_size > 0 ? long_stop_loss : na, color=color.red, 
     linewidth=1, style=plot.style_linebr, title="多头止损")
plot(strategy.position_size > 0 ? long_take_profit : na, color=color.green, 
     linewidth=1, style=plot.style_linebr, title="多头止盈")
plot(strategy.position_size < 0 ? short_stop_loss : na, color=color.red, 
     linewidth=1, style=plot.style_linebr, title="空头止损")
plot(strategy.position_size < 0 ? short_take_profit : na, color=color.green, 
     linewidth=1, style=plot.style_linebr, title="空头止盈")

// ============================================================================
// 性能统计显示
// ============================================================================

// 在图表上显示关键统计信息
var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "总交易次数", text_color=color.black)
    table.cell(info_table, 1, 0, str.tostring(strategy.closedtrades), text_color=color.black)
    table.cell(info_table, 0, 1, "胜率", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(math.round(strategy.wintrades / strategy.closedtrades * 100, 2)) + "%", text_color=color.black)
    table.cell(info_table, 0, 2, "盈亏比", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(math.round(strategy.grossprofit / math.abs(strategy.grossloss), 2)), text_color=color.black)
    table.cell(info_table, 0, 3, "最大回撤", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(math.round(strategy.max_drawdown / strategy.initial_capital * 100, 2)) + "%", text_color=color.black)
    table.cell(info_table, 0, 4, "当前仓位", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(strategy.position_size), text_color=color.black)
    table.cell(info_table, 0, 5, "RSI", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(math.round(rsi, 2)), text_color=color.black)
