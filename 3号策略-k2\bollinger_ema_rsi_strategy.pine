// @version=5
strategy("Bollinger + EMA + RSI Strategy", overlay=true, initial_capital=1000, default_qty_type=strategy.percent_of_equity, default_qty_value=10, commission_type=strategy.commission.percent, commission_value=0.075)

// --- 输入参数 ---
// 布林带设置
bb_length = input.int(20, title="BB Length", minval=1)
bb_mult = input.float(2.0, title="BB StdDev", minval=0.001, maxval=50)

// EMA 设置
ema_short_len = input.int(5, title="Short EMA")
ema_medium_len = input.int(20, title="Medium EMA")
ema_long_len = input.int(50, title="Long EMA")

// RSI 设置
rsi_length = input.int(14, title="RSI Length")
rsi_overbought = input.int(70, title="RSI Overbought")
rsi_oversold = input.int(30, title="RSI Oversold")
rsi_middle = 50

// ATR 风险管理设置
atr_length = input.int(14, title="ATR Length")
atr_sl_mult = input.float(1.5, title="ATR SL Multiplier")
atr_tp_mult = input.float(3.0, title="ATR TP Multiplier") // 风险回报比 1:2

// 杠杆
leverage = input.int(5, title="Leverage", minval=1)

// --- 指标计算 ---
// 布林带
[basis, upper, lower] = ta.bb(close, bb_length, bb_mult)

// EMA
ema_short = ta.ema(close, ema_short_len)
ema_medium = ta.ema(close, ema_medium_len)
ema_long = ta.ema(close, ema_long_len)

// RSI
rsi = ta.rsi(close, rsi_length)

// ATR
atr = ta.atr(atr_length)

// --- 交易逻辑 ---
// 条件
long_trend_condition = ema_medium > ema_long
short_trend_condition = ema_medium < ema_long

long_rsi_condition = rsi > rsi_middle
short_rsi_condition = rsi < rsi_middle

bollinger_long_breakout = ta.crossover(close, upper)
bollinger_short_breakout = ta.crossunder(close, lower)

// 最终开仓条件
go_long = long_trend_condition and long_rsi_condition and bollinger_long_breakout and close > ema_short
go_short = short_trend_condition and short_rsi_condition and bollinger_short_breakout and close < ema_short

// --- 风险管理 ---
long_stop_loss = close - (atr * atr_sl_mult)
long_take_profit = close + (atr * atr_tp_mult)

short_stop_loss = close + (atr * atr_sl_mult)
short_take_profit = close - (atr * atr_tp_mult)

// --- 执行交易 ---
if (go_long)
    strategy.entry("Long", strategy.long)
    strategy.exit("Exit Long", "Long", stop=long_stop_loss, limit=long_take_profit)

if (go_short)
    strategy.entry("Short", strategy.short)
    strategy.exit("Exit Short", "Short", stop=short_stop_loss, limit=short_take_profit)

// --- Webhook 消息 ---
// OKX Webhook alert message format
// You need to set up a third-party service (like 3Commas, Alertatron, etc.) to parse this message and send it to OKX.
// The message format might need to be adjusted based on the service you use.
long_alert_message = '{"symbol": "' + syminfo.ticker + '", "side": "buy", "type": "market", "quantity_percent": "10", "leverage": "' + str.tostring(leverage) + '", "sl": "' + str.tostring(long_stop_loss) + '", "tp": "' + str.tostring(long_take_profit) + '"}'
short_alert_message = '{"symbol": "' + syminfo.ticker + '", "side": "sell", "type": "market", "quantity_percent": "10", "leverage": "' + str.tostring(leverage) + '", "sl": "' + str.tostring(short_stop_loss) + '", "tp": "' + str.tostring(short_take_profit) + '"}'

if (go_long)
    alert(long_alert_message, alert.freq_once_per_bar_close)

if (go_short)
    alert(short_alert_message, alert.freq_once_per_bar_close)

// --- 绘图 ---
// 绘制布林带
plot(basis, "Basis", color=color.orange)
p1 = plot(upper, "Upper", color=color.blue)
p2 = plot(lower, "Lower", color=color.blue)
fill(p1, p2, title = "Background", color=color.new(color.blue, 90))

// 绘制 EMA
plot(ema_short, color=color.new(color.yellow, 0), title="Short EMA")
plot(ema_medium, color=color.new(color.green, 0), title="Medium EMA")
plot(ema_long, color=color.new(color.red, 0), title="Long EMA")

// 绘制开仓信号
plotshape(go_long, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small, title="Long Signal")
plotshape(go_short, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small, title="Short Signal")