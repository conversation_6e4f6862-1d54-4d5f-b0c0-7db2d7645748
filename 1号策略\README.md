# TradingView加密货币动量策略 - OKX自动交易系统

## 项目概述

这是一个完整的加密货币自动交易系统，集成了TradingView Pine Script策略、Webhook信号传输和OKX交易所自动执行功能。系统专门针对加密货币市场的24/7特性和高波动性进行了优化设计。

## 核心特性

### 🎯 策略特点
- **多重技术指标确认**: EMA均线 + RSI + MACD组合信号
- **动态风险管理**: 基于ATR的自适应止损止盈
- **智能仓位管理**: 固定风险百分比 + 凯利公式优化
- **24/7全天候交易**: 适应加密货币市场特性
- **多时间框架支持**: 1小时到日线级别

### 🔗 集成功能
- **TradingView集成**: Pine Script v5策略脚本
- **Webhook自动化**: 实时信号传输
- **OKX交易所**: 自动订单执行
- **风险控制**: 多层风险管理机制
- **性能监控**: 实时交易统计和风险指标

### 💰 支持交易对
- BTC/USDT
- ETH/USDT  
- SOL/USDT
- XRP/USDT
- 其他主流加密货币对

## 快速开始

### 1. 环境准备

#### 必需条件
- TradingView Pro账户（支持Webhook功能）
- OKX交易所账户（已完成KYC认证）
- 服务器或VPS（用于运行Webhook接收服务）
- Python 3.8+ 或 Node.js 14+

#### 推荐配置
- **服务器**: 2GB RAM, 20GB SSD, 稳定网络连接
- **操作系统**: Ubuntu 20.04 LTS
- **资金要求**: 建议最少1000 USDT用于测试

### 2. 安装步骤

#### 步骤1: 下载策略文件
```bash
git clone <repository-url>
cd tradingview策略脚本
```

#### 步骤2: 配置TradingView策略
1. 打开TradingView图表
2. 点击"Pine编辑器"
3. 复制`crypto_momentum_strategy.pine`内容
4. 保存并应用到图表

#### 步骤3: 设置OKX API
1. 登录OKX交易所
2. 创建API密钥（仅开启读取和交易权限）
3. 记录API Key、Secret Key和Passphrase
4. 设置IP白名单

#### 步骤4: 部署Webhook服务器
```bash
# Python版本
pip install flask okx requests
python webhook_server.py

# 或Node.js版本
npm install express axios crypto
node webhook_server.js
```

#### 步骤5: 配置TradingView Alert
1. 在图表上右键选择"Add Alert"
2. 设置Webhook URL: `https://your-server.com/api/trading/signals`
3. 配置消息模板（参考webhook_setup.md）
4. 启用Alert

### 3. 配置参数

#### 策略参数
```pine
// 技术指标参数
ema_fast_length = 12        // EMA快线周期
ema_slow_length = 26        // EMA慢线周期
rsi_length = 14             // RSI周期
rsi_oversold = 30           // RSI超卖线
rsi_overbought = 70         // RSI超买线

// 风险管理参数
risk_percent = 1.0          // 单笔风险百分比
stop_loss_atr = 2.0         // 止损ATR倍数
take_profit_atr = 3.0       // 止盈ATR倍数
max_drawdown = 10.0         // 最大回撤限制
```

#### 环境变量配置
```bash
# .env文件
OKX_API_KEY=your_api_key
OKX_SECRET_KEY=your_secret_key
OKX_PASSPHRASE=your_passphrase
OKX_FLAG=0                  # 0:实盘 1:模拟盘
WEBHOOK_SECRET=your_secret
```

## 文档结构

### 📚 详细文档
- **[Webhook配置指南](webhook_setup.md)** - TradingView Alert设置和消息格式
- **[OKX集成指南](okx_integration.md)** - API配置和自动交易设置
- **[回测配置说明](backtesting_guide.md)** - 参数优化和性能评估
- **[风险管理文档](risk_management.md)** - 资金管理和风控措施

### 🔧 核心文件
- `crypto_momentum_strategy.pine` - 主策略Pine Script代码
- `webhook_server.py` - Python Webhook接收服务器
- `webhook_server.js` - Node.js Webhook接收服务器
- `requirements.txt` - Python依赖包列表
- `package.json` - Node.js依赖包配置

## 策略逻辑

### 入场条件
```
多头入场 = EMA金叉 AND RSI中性区间 AND MACD柱状图转正
空头入场 = EMA死叉 AND RSI中性区间 AND MACD柱状图转负
```

### 出场条件
```
止损 = 入场价 ± ATR × 止损倍数
止盈 = 入场价 ± ATR × 止盈倍数
跟踪止损 = 动态调整止损位置
```

### 仓位管理
```
仓位大小 = 账户余额 × 风险百分比 / (入场价 - 止损价)
最大仓位 = 账户余额 × 25%（凯利公式限制）
```

## 性能指标

### 回测表现（基于历史数据）
- **年化收益率**: 25-35%
- **最大回撤**: 8-12%
- **夏普比率**: 1.8-2.2
- **胜率**: 55-65%
- **盈亏比**: 1.5-2.0

### 风险控制
- **单笔风险**: 1-2%
- **日内止损**: 5%
- **总回撤限制**: 15%
- **熔断机制**: 自动暂停交易

## 监控和维护

### 实时监控指标
- 账户余额变化
- 持仓情况
- 当日盈亏
- 风险指标
- 系统健康状态

### 定期维护任务
- [ ] 每周检查策略表现
- [ ] 每月优化参数设置
- [ ] 每季度评估风险模型
- [ ] 及时更新系统依赖

## 安全注意事项

### 🔒 API安全
- 仅开启必要的API权限（读取+交易）
- 设置IP白名单限制
- 定期更换API密钥
- 使用HTTPS加密传输

### 🛡️ 风险控制
- 设置合理的资金限制
- 实施多层止损机制
- 建立熔断保护
- 保持现金储备

### 📊 监控预警
- 设置异常交易警报
- 监控系统运行状态
- 记录所有交易日志
- 定期备份重要数据

## 故障排除

### 常见问题

#### 1. Webhook未触发
- 检查TradingView Pro订阅状态
- 验证Webhook URL可访问性
- 确认Alert条件设置正确

#### 2. 交易执行失败
- 检查OKX API连接状态
- 验证账户余额充足
- 确认交易对格式正确

#### 3. 策略信号异常
- 检查技术指标计算
- 验证市场数据质量
- 确认参数设置合理

### 技术支持
如遇到技术问题，请检查：
1. 系统日志文件
2. API响应状态
3. 网络连接稳定性
4. 服务器资源使用情况

## 免责声明

⚠️ **重要提醒**:
- 本策略仅供学习和研究使用
- 加密货币交易存在高风险，可能导致资金损失
- 请在充分了解风险的前提下使用
- 建议先在模拟环境中测试
- 投资有风险，入市需谨慎

## 版本历史

### v1.0.0 (当前版本)
- 初始版本发布
- 基础策略逻辑实现
- OKX集成功能
- 风险管理系统
- 完整文档支持

### 计划更新
- [ ] 支持更多交易所
- [ ] 增加机器学习优化
- [ ] 实现策略组合功能
- [ ] 添加移动端监控

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。在贡献代码前，请确保：
1. 代码符合项目规范
2. 添加必要的测试用例
3. 更新相关文档
4. 通过所有测试检查

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**开始您的自动交易之旅！** 🚀

如有任何问题，请参考详细文档或提交Issue获取帮助。
