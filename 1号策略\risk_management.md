# 风险管理指南

## 概述

风险管理是自动交易系统的核心组成部分。本指南详细说明如何实施有效的风险控制措施，保护交易资金安全。

## 风险管理原则

### 1. 核心原则

1. **资本保护优先**: 保护本金比追求利润更重要
2. **风险分散**: 不要把所有资金投入单一策略或交易对
3. **严格执行**: 风险规则必须严格执行，不能因情绪而改变
4. **持续监控**: 实时监控风险指标和系统状态
5. **适应性调整**: 根据市场条件调整风险参数

### 2. 风险类型识别

#### 市场风险
- 价格波动风险
- 流动性风险
- 系统性风险

#### 技术风险
- 系统故障风险
- 网络连接风险
- API限制风险

#### 操作风险
- 参数设置错误
- 策略逻辑缺陷
- 人为操作失误

## 仓位管理策略

### 1. 固定风险百分比法

```python
def calculate_position_size_fixed_risk(account_balance, risk_percent, entry_price, stop_loss):
    """
    基于固定风险百分比计算仓位大小
    
    Args:
        account_balance: 账户余额
        risk_percent: 风险百分比 (1-2%)
        entry_price: 入场价格
        stop_loss: 止损价格
    
    Returns:
        position_size: 仓位大小
    """
    risk_amount = account_balance * risk_percent / 100
    price_diff = abs(entry_price - stop_loss)
    position_size = risk_amount / price_diff
    return position_size

# 示例使用
account_balance = 10000  # USDT
risk_percent = 1.0       # 1%
entry_price = 45000      # BTC价格
stop_loss = 44000        # 止损价格

position_size = calculate_position_size_fixed_risk(
    account_balance, risk_percent, entry_price, stop_loss
)
print(f"建议仓位大小: {position_size:.6f} BTC")
```

### 2. 凯利公式法

```python
def kelly_criterion(win_rate, avg_win, avg_loss):
    """
    凯利公式计算最优仓位比例
    
    Args:
        win_rate: 胜率
        avg_win: 平均盈利
        avg_loss: 平均亏损
    
    Returns:
        kelly_percent: 凯利百分比
    """
    if avg_loss == 0:
        return 0
    
    win_loss_ratio = avg_win / avg_loss
    kelly_percent = win_rate - (1 - win_rate) / win_loss_ratio
    
    # 限制最大仓位为25%（保守策略）
    return min(kelly_percent, 0.25)

# 示例计算
win_rate = 0.6      # 60%胜率
avg_win = 150       # 平均盈利150 USDT
avg_loss = 100      # 平均亏损100 USDT

kelly_size = kelly_criterion(win_rate, avg_win, avg_loss)
print(f"凯利公式建议仓位: {kelly_size:.2%}")
```

### 3. ATR动态仓位法

```python
def calculate_atr_position_size(account_balance, atr_value, atr_multiplier, risk_percent):
    """
    基于ATR计算动态仓位大小
    
    Args:
        account_balance: 账户余额
        atr_value: ATR值
        atr_multiplier: ATR倍数
        risk_percent: 风险百分比
    
    Returns:
        position_size: 仓位大小
    """
    risk_amount = account_balance * risk_percent / 100
    stop_distance = atr_value * atr_multiplier
    position_size = risk_amount / stop_distance
    return position_size
```

## 止损策略

### 1. 固定百分比止损

```pine
// Pine Script中的固定百分比止损
stop_loss_percent = input.float(2.0, title="止损百分比", minval=0.5, maxval=10.0)

long_stop_loss = strategy.position_avg_price * (1 - stop_loss_percent / 100)
short_stop_loss = strategy.position_avg_price * (1 + stop_loss_percent / 100)

if strategy.position_size > 0
    strategy.exit("Long Stop", "Long", stop=long_stop_loss)
if strategy.position_size < 0
    strategy.exit("Short Stop", "Short", stop=short_stop_loss)
```

### 2. ATR动态止损

```pine
// ATR动态止损
atr_length = input.int(14, title="ATR周期")
atr_multiplier = input.float(2.0, title="ATR倍数")

atr_value = ta.atr(atr_length)

long_stop_loss = close - (atr_value * atr_multiplier)
short_stop_loss = close + (atr_value * atr_multiplier)
```

### 3. 跟踪止损

```pine
// 跟踪止损实现
trailing_percent = input.float(1.5, title="跟踪止损百分比")

var float long_trailing_stop = na
var float short_trailing_stop = na

if strategy.position_size > 0
    if na(long_trailing_stop) or close > nz(long_trailing_stop) / (1 - trailing_percent / 100)
        long_trailing_stop := close * (1 - trailing_percent / 100)
    strategy.exit("Long Trail", "Long", stop=long_trailing_stop)

if strategy.position_size < 0
    if na(short_trailing_stop) or close < nz(short_trailing_stop) / (1 + trailing_percent / 100)
        short_trailing_stop := close * (1 + trailing_percent / 100)
    strategy.exit("Short Trail", "Short", stop=short_trailing_stop)
```

## 风险监控指标

### 1. 实时风险指标

```python
class RiskMonitor:
    def __init__(self, initial_balance):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.max_balance = initial_balance
        self.trades = []
        
    def update_balance(self, new_balance):
        """更新账户余额"""
        self.current_balance = new_balance
        if new_balance > self.max_balance:
            self.max_balance = new_balance
    
    def calculate_drawdown(self):
        """计算当前回撤"""
        return (self.max_balance - self.current_balance) / self.max_balance
    
    def calculate_var(self, confidence_level=0.05):
        """计算风险价值(VaR)"""
        if len(self.trades) < 30:
            return None
        
        returns = [trade['return'] for trade in self.trades[-30:]]
        returns.sort()
        var_index = int(len(returns) * confidence_level)
        return returns[var_index]
    
    def check_risk_limits(self):
        """检查风险限制"""
        alerts = []
        
        # 检查最大回撤
        current_drawdown = self.calculate_drawdown()
        if current_drawdown > 0.15:  # 15%回撤警告
            alerts.append(f"回撤警告: {current_drawdown:.2%}")
        
        # 检查连续亏损
        recent_trades = self.trades[-5:] if len(self.trades) >= 5 else self.trades
        if all(trade['return'] < 0 for trade in recent_trades):
            alerts.append("连续亏损警告")
        
        return alerts
```

### 2. 风险指标计算

```python
def calculate_risk_metrics(returns):
    """计算风险指标"""
    import numpy as np
    
    returns = np.array(returns)
    
    metrics = {
        'volatility': np.std(returns) * np.sqrt(252),  # 年化波动率
        'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252),
        'max_drawdown': calculate_max_drawdown(returns),
        'var_95': np.percentile(returns, 5),  # 95% VaR
        'cvar_95': np.mean(returns[returns <= np.percentile(returns, 5)])  # 条件VaR
    }
    
    return metrics

def calculate_max_drawdown(returns):
    """计算最大回撤"""
    cumulative = np.cumprod(1 + returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = (cumulative - running_max) / running_max
    return np.min(drawdown)
```

## 资金管理规则

### 1. 分层资金管理

```python
class CapitalAllocation:
    def __init__(self, total_capital):
        self.total_capital = total_capital
        self.allocations = {
            'core_strategy': 0.60,      # 60% 核心策略
            'aggressive_strategy': 0.20, # 20% 激进策略
            'hedge_positions': 0.10,     # 10% 对冲仓位
            'cash_reserve': 0.10         # 10% 现金储备
        }
    
    def get_allocation(self, strategy_type):
        """获取策略分配资金"""
        return self.total_capital * self.allocations.get(strategy_type, 0)
    
    def adjust_allocation(self, performance_metrics):
        """根据表现调整资金分配"""
        if performance_metrics['sharpe_ratio'] > 2.0:
            # 表现优秀，增加核心策略分配
            self.allocations['core_strategy'] = min(0.70, 
                self.allocations['core_strategy'] + 0.05)
        elif performance_metrics['max_drawdown'] > 0.15:
            # 回撤过大，增加现金储备
            self.allocations['cash_reserve'] = min(0.20,
                self.allocations['cash_reserve'] + 0.05)
```

### 2. 动态杠杆调整

```python
def calculate_dynamic_leverage(volatility, target_volatility=0.15):
    """
    根据市场波动率动态调整杠杆
    
    Args:
        volatility: 当前市场波动率
        target_volatility: 目标波动率
    
    Returns:
        leverage: 建议杠杆倍数
    """
    if volatility == 0:
        return 1.0
    
    leverage = target_volatility / volatility
    
    # 限制杠杆范围
    return max(0.5, min(3.0, leverage))

# 示例使用
current_volatility = 0.25  # 当前25%波动率
recommended_leverage = calculate_dynamic_leverage(current_volatility)
print(f"建议杠杆倍数: {recommended_leverage:.2f}x")
```

## 紧急风控措施

### 1. 熔断机制

```python
class CircuitBreaker:
    def __init__(self, daily_loss_limit=0.05, total_loss_limit=0.20):
        self.daily_loss_limit = daily_loss_limit
        self.total_loss_limit = total_loss_limit
        self.daily_start_balance = None
        self.initial_balance = None
        self.is_triggered = False
    
    def check_limits(self, current_balance):
        """检查是否触发熔断"""
        if self.daily_start_balance is None:
            self.daily_start_balance = current_balance
        if self.initial_balance is None:
            self.initial_balance = current_balance
        
        # 检查日内亏损
        daily_loss = (self.daily_start_balance - current_balance) / self.daily_start_balance
        if daily_loss > self.daily_loss_limit:
            self.is_triggered = True
            return "日内亏损熔断"
        
        # 检查总亏损
        total_loss = (self.initial_balance - current_balance) / self.initial_balance
        if total_loss > self.total_loss_limit:
            self.is_triggered = True
            return "总亏损熔断"
        
        return None
    
    def reset_daily(self, current_balance):
        """重置日内计数"""
        self.daily_start_balance = current_balance
        self.is_triggered = False
```

### 2. 异常检测

```python
def detect_anomalies(recent_returns, threshold=3.0):
    """检测异常交易结果"""
    import numpy as np
    
    if len(recent_returns) < 10:
        return False
    
    mean_return = np.mean(recent_returns)
    std_return = np.std(recent_returns)
    
    latest_return = recent_returns[-1]
    z_score = abs(latest_return - mean_return) / std_return
    
    return z_score > threshold

# 示例使用
recent_returns = [-0.02, 0.01, -0.01, 0.03, -0.15]  # 最后一个异常
is_anomaly = detect_anomalies(recent_returns)
if is_anomaly:
    print("检测到异常交易结果，建议暂停交易")
```

## 风险报告和监控

### 1. 风险报告模板

```python
def generate_risk_report(account_data, trades_data):
    """生成风险报告"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'account_balance': account_data['balance'],
        'total_exposure': account_data['total_exposure'],
        'current_drawdown': calculate_drawdown(account_data),
        'daily_pnl': calculate_daily_pnl(trades_data),
        'risk_metrics': calculate_risk_metrics(trades_data),
        'position_summary': get_position_summary(account_data),
        'alerts': check_risk_alerts(account_data, trades_data)
    }
    return report

def send_risk_alert(alert_message):
    """发送风险警报"""
    # 可以通过邮件、短信、钉钉等方式发送
    print(f"风险警报: {alert_message}")
    # 实际实现中可以集成邮件或消息推送服务
```

### 2. 实时监控仪表板

```python
# 使用Streamlit创建监控仪表板示例
import streamlit as st
import plotly.graph_objects as go

def create_risk_dashboard():
    st.title("交易风险监控仪表板")
    
    # 账户概览
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("账户余额", "10,000 USDT", "2.5%")
    with col2:
        st.metric("当日盈亏", "+150 USDT", "1.5%")
    with col3:
        st.metric("最大回撤", "8.5%", "-1.2%")
    with col4:
        st.metric("夏普比率", "1.85", "0.15")
    
    # 风险指标图表
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=list(range(30)),
        y=[random.uniform(-0.05, 0.05) for _ in range(30)],
        name="日收益率"
    ))
    st.plotly_chart(fig)
```

## 最佳实践建议

### 1. 风险管理检查清单

- [ ] 设置合理的单笔风险限制（1-2%）
- [ ] 实施多层止损机制
- [ ] 建立熔断机制
- [ ] 定期评估和调整风险参数
- [ ] 保持充足的现金储备
- [ ] 监控系统健康状态
- [ ] 建立应急预案

### 2. 常见风险管理错误

1. **过度自信**: 连续盈利后增加风险敞口
2. **报复性交易**: 亏损后试图快速回本
3. **忽视相关性**: 多个策略投资相关资产
4. **参数频繁调整**: 根据短期表现修改策略
5. **缺乏监控**: 没有实时监控风险指标

### 3. 风险管理文化

- 建立风险优先的交易文化
- 定期进行风险评估和压力测试
- 保持学习和改进的态度
- 记录和分析所有风险事件
- 与其他交易者分享经验教训

## 下一步

完成风险管理设置后，请参考：
- [回测配置说明](backtesting_guide.md) - 验证风险控制效果
- [OKX集成指南](okx_integration.md) - 实施自动风控
- [项目README](README.md) - 了解完整系统架构
