import random
import string
import argparse


def random_email(domain="usa.edu.pl"):
    local_len = random.randint(6, 12)
    local_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=local_len))
    return f"{local_part}@{domain}"


def random_recovery_key(prefix_len=7, digit_len=7):
    prefix = ''.join(random.choices(string.ascii_uppercase, k=prefix_len))
    digits = ''.join(random.choices(string.digits, k=digit_len))
    return prefix + digits


def generate_accounts_failed(n):
    emails = set()
    results = []
    for i in range(1, n + 1):
        email = random_email()
        while email in emails:
            email = random_email()
        emails.add(email)
        recovery = random_recovery_key()
        cost = random.randint(180, 300)
        block = (
            f"第{i}个账号：\n"
            f"正在注册...\n"
            f"等待验证码...\n"
            f"注册成功！\n"
            f"社区版转换失败...没有找到社区选项\n"
            f"刷新重试中...\n"
            f"社区版转换失败...没有找到社区选项\n"
            f"刷新重试中...\n"
            f"账单生成成功！\n"
            f"账单上传成功！\n"
            f"当前AUG额度剩余：0/725\n"
            f"邮箱：\n"
            f"{email}\n"
            f"恢复密钥：\n"
            f"{recovery}\n"
            f"保存邮箱和密钥信息成功！\n"
            f"重置环境中...\n"
            f"重置环境成功！\n"
            f"耗时{cost}s\n"
        )
        results.append(block)
    return "\n\n".join(results)


def positive_int(value):
    try:
        ivalue = int(value)
    except ValueError:
        raise argparse.ArgumentTypeError("请输入正整数数量")
    if ivalue <= 0:
        raise argparse.ArgumentTypeError("数量必须是大于 0 的正整数")
    return ivalue


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="生成失败版：邮箱+恢复密钥账号记录（社区版转换失败，AUG额度0/725）")
    parser.add_argument("N", type=positive_int, nargs="?", help="要生成的账号数量（正整数）")
    args = parser.parse_args()

    if args.N is not None:
        N = args.N
    else:
        while True:
            user_in = input("请输入要生成的账号数量（正整数）：").strip()
            try:
                N = positive_int(user_in)
                break
            except argparse.ArgumentTypeError as e:
                print(str(e))
                continue

    output = generate_accounts_failed(N)
    print(output)