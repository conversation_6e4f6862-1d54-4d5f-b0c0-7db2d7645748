# TradingView Webhook配置指南

## 概述

本指南详细说明如何在TradingView中设置Webhook警报，以便与OKX交易所进行自动交易集成。

## 前置条件

1. TradingView Pro账户（支持Webhook功能）
2. 已部署的Webhook接收服务器
3. OKX交易所API密钥
4. 已加载的加密货币动量策略脚本

## Webhook消息格式

### 开仓消息格式

#### 多头开仓
```json
{
  "action": "buy",
  "symbol": "BTC-USDT",
  "type": "market",
  "size": "0.001",
  "price": "45000.00",
  "timestamp": "2024-01-15T10:30:00Z",
  "strategy": "crypto_momentum",
  "stop_loss": "44000.00",
  "take_profit": "47000.00"
}
```

#### 空头开仓
```json
{
  "action": "sell",
  "symbol": "BTC-USDT", 
  "type": "market",
  "size": "0.001",
  "price": "45000.00",
  "timestamp": "2024-01-15T10:30:00Z",
  "strategy": "crypto_momentum",
  "stop_loss": "46000.00",
  "take_profit": "43000.00"
}
```

#### 平仓消息
```json
{
  "action": "close",
  "symbol": "BTC-USDT",
  "type": "market", 
  "size": "0.001",
  "price": "45500.00",
  "timestamp": "2024-01-15T11:30:00Z",
  "strategy": "crypto_momentum"
}
```

## TradingView Alert设置步骤

### 1. 创建Alert

1. 在TradingView图表上右键点击
2. 选择"Add Alert"或点击顶部工具栏的闹钟图标
3. 在Alert设置窗口中进行以下配置：

### 2. Alert基本配置

- **Condition**: 选择"crypto_momentum_strategy"
- **Options**: 根据需要选择触发条件
  - 多头开仓：选择"多头开仓"
  - 空头开仓：选择"空头开仓"  
  - 平仓：选择"平仓"

### 3. Webhook URL配置

在"Webhook URL"字段中输入您的Webhook接收服务器地址：
```
https://your-webhook-server.com/api/trading/signals
```

### 4. 消息模板配置

#### 多头开仓Alert消息
```json
{
  "action": "buy",
  "symbol": "{{symbol}}",
  "type": "market",
  "size": "{{strategy.position_size}}",
  "price": "{{close}}",
  "timestamp": "{{time}}",
  "strategy": "crypto_momentum",
  "stop_loss": "{{plot_01}}",
  "take_profit": "{{plot_02}}"
}
```

#### 空头开仓Alert消息
```json
{
  "action": "sell",
  "symbol": "{{symbol}}",
  "type": "market", 
  "size": "{{strategy.position_size}}",
  "price": "{{close}}",
  "timestamp": "{{time}}",
  "strategy": "crypto_momentum",
  "stop_loss": "{{plot_03}}",
  "take_profit": "{{plot_04}}"
}
```

#### 平仓Alert消息
```json
{
  "action": "close",
  "symbol": "{{symbol}}",
  "type": "market",
  "size": "{{strategy.position_size}}",
  "price": "{{close}}",
  "timestamp": "{{time}}",
  "strategy": "crypto_momentum"
}
```

## TradingView变量说明

| 变量 | 说明 | 示例值 |
|------|------|--------|
| {{symbol}} | 当前交易对 | BTCUSDT |
| {{close}} | 当前收盘价 | 45000.50 |
| {{time}} | 当前时间戳 | 2024-01-15T10:30:00Z |
| {{strategy.position_size}} | 策略仓位大小 | 0.001 |
| {{plot_01}} | 自定义绘图值1 | 44000.00 |
| {{plot_02}} | 自定义绘图值2 | 47000.00 |

## Alert频率设置

- **Frequency**: 建议设置为"Once Per Bar Close"
- **Expiration**: 根据策略需要设置，建议"Open-ended"

## 安全考虑

### 1. Webhook URL保护
- 使用HTTPS协议
- 实施API密钥验证
- 添加IP白名单限制
- 使用签名验证消息完整性

### 2. 消息验证
```python
# Python示例：验证Webhook消息
import hmac
import hashlib

def verify_webhook_signature(payload, signature, secret):
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(signature, expected_signature)
```

### 3. 错误处理
- 实施重试机制
- 记录所有交易信号
- 设置失败通知
- 监控系统健康状态

## 测试配置

### 1. 测试Webhook连接
```bash
curl -X POST https://your-webhook-server.com/api/trading/signals \
  -H "Content-Type: application/json" \
  -d '{
    "action": "test",
    "symbol": "BTC-USDT",
    "timestamp": "2024-01-15T10:30:00Z"
  }'
```

### 2. 模拟交易测试
1. 首先在测试环境中验证Webhook
2. 使用小额资金进行实盘测试
3. 监控所有交易信号的执行情况
4. 验证止损止盈设置是否正确

## 常见问题排查

### 1. Webhook未触发
- 检查TradingView Pro订阅状态
- 验证Webhook URL是否可访问
- 确认Alert条件设置正确
- 检查策略是否正常运行

### 2. 消息格式错误
- 验证JSON格式是否正确
- 检查变量替换是否正常
- 确认所有必需字段都已包含

### 3. 交易执行失败
- 检查OKX API连接状态
- 验证交易对名称格式
- 确认账户余额充足
- 检查仓位大小计算是否正确

## 监控和日志

### 1. 建议监控指标
- Webhook接收成功率
- 交易执行成功率
- 平均响应时间
- 错误率统计

### 2. 日志记录
```python
# 示例日志格式
{
  "timestamp": "2024-01-15T10:30:00Z",
  "webhook_id": "wh_123456",
  "signal_type": "buy",
  "symbol": "BTC-USDT",
  "price": 45000.50,
  "size": 0.001,
  "status": "success",
  "execution_time": 150
}
```

## 下一步

配置完成后，请参考：
- [OKX集成指南](okx_integration.md) - 设置OKX API和自动交易
- [风险管理文档](risk_management.md) - 了解风险控制措施
- [回测配置说明](backtesting_guide.md) - 优化策略参数
